{"name": "social_media_apis", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.ts", "dev": "nodemon index.ts"}, "repository": {"type": "git", "url": "git+https://github.com/punitDT/social_media_apis.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/punitDT/social_media_apis/issues"}, "homepage": "https://github.com/punitDT/social_media_apis#readme", "dependencies": {"@types/jsonwebtoken": "^9.0.10", "bcrypt": "^6.0.0", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.3", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7"}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/express": "^5.0.3", "nodemon": "^3.1.10", "sequelize-cli": "^6.6.3"}}