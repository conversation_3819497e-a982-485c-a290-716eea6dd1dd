name: Deploy Node App to EC2

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.EC2_SSH_KEY }}

      - name: Deploy to EC2 (Amazon Linux)
        run: |
          ssh -o StrictHostKeyChecking=no ec2-user@${{ secrets.EC2_PUBLIC_IP }} << 'EOF'
            # Navigate to or clone the app
            cd app || git clone https://github.com/punitDT/social_media_apis.git app && cd app

            # Pull latest changes
            git pull origin main

            # Set environment variables
            echo "DATABASE_URL=${{ secrets.DATABASE_URL }}" > .env

            # Build and run Docker containers
            docker-compose down
            docker-compose up -d --build
          EOF
